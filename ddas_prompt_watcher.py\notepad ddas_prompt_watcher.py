import os
import time
import hashlib
import json
import tkinter as tk
from tkinter import messagebox
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from datetime import datetime

LOG_FILE = "ddas_log.json"
WATCH_FOLDER = os.path.expanduser("~/Downloads")  # You can change this

def get_file_hash(file_path):
    with open(file_path, "rb") as f:
        return hashlib.sha256(f.read()).hexdigest()

def load_log():
    if not os.path.exists(LOG_FILE):
        return []
    with open(LOG_FILE, "r") as f:
        return json.load(f)

def save_log(log):
    with open(LOG_FILE, "w") as f:
        json.dump(log, f, indent=4)

def prompt_user(filename):
    root = tk.Tk()
    root.withdraw()  # hide main window
    answer = messagebox.askyesno(
        title="Duplicate Download Detected",
        message=f"The file '{filename}' was already downloaded.\nDo you want to keep it?"
    )
    root.destroy()
    return answer  # True if Yes, False if No

class Watcher(FileSystemEventHandler):
    def on_created(self, event):
        if event.is_directory:
            return
        file_path = event.src_path
        try:
            time.sleep(1.5)  # Wait for file to fully download
            file_hash = get_file_hash(file_path)
            log = load_log()
            for entry in log:
                if entry["hash"] == file_hash:
                    print(f"⚠️ Duplicate detected: {file_path}")
                    keep = prompt_user(os.path.basename(file_path))
                    if not keep:
                        os.remove(file_path)
                        print("🗑️ File removed by user decision.")
                    else:
                        print("✅ File kept by user.")
                    return
            # Log the new file
            log.append({
                "filename": os.path.basename(file_path),
                "hash": file_hash,
                "timestamp": datetime.now().isoformat()
            })
            save_log(log)
            print(f"📥 New file logged: {file_path}")
        except Exception as e:
            print(f"⚠️ Error: {e}")

def main():
    print(f"👁️ Watching downloads in: {WATCH_FOLDER}")
    observer = Observer()
    observer.schedule(Watcher(), WATCH_FOLDER, recursive=False)
    observer.start()
    try:
        while True:
            time.sleep(5)
    except KeyboardInterrupt:
        observer.stop()
    observer.join()

if __name__ == "__main__":
    main()
